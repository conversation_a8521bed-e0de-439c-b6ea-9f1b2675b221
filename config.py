# Konfigurationskonstanten für den Theo Cha<PERSON>bot

# Backend URLs
BACKEND_BASE_URL = "http://localhost:8081"
CHAT_ENDPOINT = f"{BACKEND_BASE_URL}/chat"
CLEAR_ENDPOINT = f"{BACKEND_BASE_URL}/clear"
APPROVE_ENDPOINT = f"{BACKEND_BASE_URL}/approve"
DISAPPROVE_ENDPOINT = f"{BACKEND_BASE_URL}/disapprove"

# OAuth2 Konfiguration
KEYCLOAK_BASE_URL = "http://localhost:8080"
CLIENT_ID = "theo-chatbot-server"
CLIENT_SECRET = "********************************"
AUTHORIZE_URL = f"{KEYCLOAK_BASE_URL}/realms/test-realm/protocol/openid-connect/auth"
TOKEN_URL = f"{KEYCLOAK_BASE_URL}/realms/test-realm/protocol/openid-connect/token"
REDIRECT_URI = "http://localhost:8501/component/streamlit_oauth.oauth_component/index.html"
SCOPE = "openid"

# UI Texte
LOGIN_TITLE = "Prüfe Anmeldung..."
LOGIN_MESSAGE = "Wenn du nicht automatisch weitergeleitet wirst, klicke bitte auf den Button."
CHATBOT_TITLE = "Theo Chatbot"
CHAT_INPUT_PLACEHOLDER = "Type your message"
AUTHORIZE_BUTTON_TEXT = "Authorize"
APPROVE_BUTTON_TEXT = "Approve"
REJECT_BUTTON_TEXT = "Reject"

# Error Messages
TOKEN_REFRESH_MESSAGE = "Dein Token ist abgelaufen. Bitte lade die Seite neu, um dich erneut anzumelden."
NETWORK_ERROR_MESSAGE = "Verbindungsfehler zum Server. Bitte prüfe deine Internetverbindung und versuche es erneut."
GENERAL_ERROR_MESSAGE = "Ein unerwarteter Fehler ist aufgetreten. Bitte lade die Seite neu."
