import streamlit as st
from streamlit_oauth import OA<PERSON>2<PERSON>omponent
from dateutil import parser
import requests
import json
import time
import re
from typing import Dict, List, Optional

from config import (
    CHAT_ENDPOINT, CLEAR_ENDPOINT, APPROVE_ENDPOINT, DISAPPROVE_ENDPOINT,
    CLIENT_ID, CLIENT_SECRET, AUTHORIZE_URL, TOKEN_URL, REDIRECT_URI, SCOPE,
    LOGIN_TITLE, LOGIN_MESSAGE, CHATBOT_TITLE, CHAT_INPUT_PLACEHOLDER,
    AUTHORIZE_BUTTON_TEXT, APPROVE_BUTTON_TEXT, REJECT_BUTTON_TEXT,
    TOKEN_REFRESH_MESSAGE, NETWORK_ERROR_MESSAGE, GENERAL_ERROR_MESSAGE
)

# --- Hilfsfunktionen --------------------------------------------------------

def get_jwt_token_from_sidebar(default_token: str) -> str:
    """Liest das JWT Token aus der Sidebar ein."""
    st.sidebar.title("Configuration")
    return st.sidebar.text_input("Enter JWT Token:", value=default_token, type="password")

def display_chat_history(messages):
    """Zeigt den bisherigen Chatverlauf an."""
    for i, message in enumerate(messages):
        with st.chat_message(message["role"]):
            st.markdown(re.sub("<think>[\S\s]*</think>", "", message["content"]))

def add_user_message_to_history(user_input: str):
    """Fügt eine User-Nachricht dem State hinzu und zeigt sie an."""
    st.session_state["messages"].append({"role": "user", "content": user_input})
    with st.chat_message("user"):
        st.markdown(user_input)

def send_request_to_backend(user_input: str, jwt_token: str) -> requests.Response:
    """Sendet eine Nutzereingabe an das Backend und gibt die Response zurück."""
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "text/plain"
    }
    response = requests.post(
        "http://localhost:8081/chat",
        headers=headers,
        data=user_input,
        stream=True
    )
    response.raise_for_status()
    return response

def clear_chat_history_backend(jwt):
    headers = {
        "Authorization": f"Bearer {jwt_token}"
    }
    response = requests.get(
        "http://localhost:8081/clear",
        headers=headers,
    )
    response.raise_for_status()

def handle_approval(next_message, action: str, approval_request: dict, jwt_token: str):
    """Sendet eine Genehmigung oder Ablehnung an das Backend."""
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    url = f"http://localhost:8081/{'approve' if action == 'approve' else 'disapprove'}"

    try:
        response = requests.post(url, headers=headers, json=approval_request, stream=True)
        response.raise_for_status()
        process_response(next_message, response, jwt_token)
    except requests.exceptions.RequestException as e:
        st.error(f"Error: {e}")

def show_approval_buttons(next_message, approval_request: dict, jwt_token: str):
    """Zeigt die Approve/Reject Buttons an."""
    col1, col2 = st.columns(2)
    col1.button(
        "Approve",
        key=f"approve_{time.time()}",
        on_click=handle_approval,
        args=(next_message, "approve", approval_request, jwt_token)
    )
    col2.button(
        "Reject",
        key=f"reject_{time.time()}",
        on_click=handle_approval,
        args=(next_message, "reject", approval_request, jwt_token)
    )

def json_to_markdown_table(json_string):
    # JSON-String in Python-Datenstruktur umwandeln
    arguments = json.loads(json_string["arguments"])
    bookings = arguments["bookings"]
    print(bookings)

    # Funktion, um das Datum und die Zeit zu formatieren
    def format_date(date):
        month_names = ["", "Januar", "Februar", "März", "April", "Mai", "Juni", "Juli", "August", "September", "Oktober", "November", "Dezember"]
        return f"{int(date.day)} {month_names[int(date.month)]}"

    def format_time(start_time, end_time):
        return "{:02d}:{:02d} - {:02d}:{:02d}".format(
            int(start_time.hour), int(start_time.minute),
            int(end_time.hour), int(end_time.minute)
        )

    # Markdown-Tabelle erstellen
    md_table = "| Zeit       | Datum       | Zeitkonto       | Beschreibung       |\n"
    md_table += "|------------|-------------|-----------------|--------------------|\n"

    for booking in bookings:
        start_time = parser.parse(booking['start']).time()
        end_time = parser.parse(booking['end']).time()
        date = parser.parse(booking['start']).date()
        time_account = booking['timeAccount']
        description = booking['description']

        time_str = format_time(start_time, end_time)
        date_str = format_date(date)

        md_table += f"| {time_str}     | {date_str}     | {time_account}     | {description}     |\n"

    return md_table

def process_response(placeholder, response: requests.Response, jwt_token: str):
    """Verarbeitet die Streaming-Antwort vom Backend und aktualisiert den Chat."""
    complete_message = ""
    approval_request_data = None

    current_message = placeholder.container()
    next_message = st.empty()

    with current_message:
        # Platzhalter für die neue Nachricht und Buttoncontainer
        message_placeholder = st.empty()
        button_container = st.empty()

        for line in response.iter_lines():
            if not line:
                continue
            decoded_line = line.decode("utf-8")
            message = json.loads(decoded_line)

            # Textnachricht anzeigen
            if "text" in message:
                complete_message += message["text"]
                with message_placeholder.container():
                    with st.chat_message("assistant"):
                        st.markdown(re.sub("<think>[\S\s]*</think>", "", complete_message))

            # ToolApprovalRequests behandeln
            elif "toolApprovalRequests" in message and message["toolApprovalRequests"]:
                approval_request_data = message["toolApprovalRequests"][0]
                if approval_request_data["name"] == "makeBookings":
                    text = json_to_markdown_table(approval_request_data)
                else:
                    text = f"```json\n{json.dumps(approval_request_data, indent=2)}\n```"
                complete_message += "\n" + text
                with message_placeholder.container():
                    with st.chat_message("assistant"):
                        st.markdown(re.sub("<think>[\S\s]*</think>", "", complete_message))

                # Nur Buttons anzeigen, wenn noch nicht hinzugefügt
                if not any(msg.get("approvalRequests") == approval_request_data
                        for msg in st.session_state["messages"]):
                    with button_container.container():
                        show_approval_buttons(next_message, approval_request_data, jwt_token)

        # Nachricht in Historie speichern
        st.session_state["messages"].append({
            "role": "assistant",
            "content": complete_message,
            "approvalRequests": approval_request_data
        })

# --- Hauptlogik -------------------------------------------------------------

CLIENT_ID="theo-chatbot-server"
CLIENT_SECRET="********************************"
AUTHORIZE_URL="http://localhost:8080/realms/test-realm/protocol/openid-connect/auth"
TOKEN_URL="http://localhost:8080/realms/test-realm/protocol/openid-connect/token"
REDIRECT_URI="http://localhost:8501/component/streamlit_oauth.oauth_component/index.html"  # <-- Das ist der Fix!
SCOPE="openid"

# Create OAuth2Component instance
oauth2 = OAuth2Component(CLIENT_ID, CLIENT_SECRET, AUTHORIZE_URL, TOKEN_URL)

# Check if token exists in session state
if 'token' not in st.session_state:

    st.title("Prüfe Anmeldung...")
    st.write("Wenn du nicht automatisch weitergeleitet wirst, klicke bitte auf den Button.")

    # If not, show authorize button
    result = oauth2.authorize_button("Authorize", REDIRECT_URI, SCOPE, auto_click=True)
    if result and 'token' in result:
        # If authorization successful, save token in session state
        st.session_state.token = result.get('token')
        st.rerun()
else:
    # If token exists in session state, show the token
    jwt_token = st.session_state['token']["access_token"]

    # Session State initialisieren
    if "messages" not in st.session_state:
        st.session_state["messages"] = []
        clear_chat_history_backend(jwt_token)

    st.title("Theo Chatbot")

    # Bisherigen Chatverlauf anzeigen
    display_chat_history(st.session_state["messages"])

    # Nutzereingabe entgegennehmen
    user_input = st.chat_input("Type your message")

    # Wenn Nutzereingabe und JWT vorhanden, an Backend senden
    if user_input and jwt_token:
        add_user_message_to_history(user_input)
        try:
            placeholder = st.empty()
            response = send_request_to_backend(user_input, jwt_token)
            process_response(placeholder, response, jwt_token)
        except requests.exceptions.RequestException as e:
            st.error(f"Error: {e}")
    else:
        if not jwt_token:
            st.sidebar.warning("Please enter a JWT token.")
