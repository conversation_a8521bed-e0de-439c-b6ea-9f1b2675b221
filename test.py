import streamlit as st
from streamlit_oauth import OAuth2Component
import requests as r

CLIENT_ID="theo-chatbot-server"
CLIENT_SECRET="********************************"
AUTHORIZE_URL="http://localhost:8080/realms/test-realm/protocol/openid-connect/auth"
TOKEN_URL="http://localhost:8080/realms/test-realm/protocol/openid-connect/token"
REDIRECT_URI="http://localhost:8501/component/streamlit_oauth.oauth_component/index.html"  # <-- Das ist der Fix!
SCOPE="openid"

# Create OAuth2Component instance
oauth2 = OAuth2Component(CLIENT_ID, CLIENT_SECRET, AUTHORIZE_URL, TOKEN_URL)

# Check if token exists in session state
if 'token' not in st.session_state:
    # If not, show authorize button
    result = oauth2.authorize_button("Authorize", REDIRECT_URI, SCOPE, auto_click=True)
    if result and 'token' in result:
        # If authorization successful, save token in session state
        st.session_state.token = result.get('token')
        st.rerun()
else:
    # If token exists in session state, show the token
    token = st.session_state['token']
    st.json(token)
    if st.button("Refresh Token"):
        # If refresh token button is clicked, refresh the token
        token = oauth2.refresh_token(token)
        st.session_state.token = token
        st.rerun()